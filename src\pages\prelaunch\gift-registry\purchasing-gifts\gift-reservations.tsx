import { useState, useEffect } from "react";
import avatar from "../../../../assets/images/avatar.png";
import delivery from "../../../../assets/images/delivery.png";
import { Button } from "../../../../components/button/onboardingButton";
import { ArrowCircleRight2 } from "iconsax-react";
import { SuccessPayment } from "./success";
import { JumiaRedirect } from "./redirect-jumia";
import { PurchaseModal } from "./purchase-modal";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import {
  EventDetailsResponse,
  GuestGiftsAPI,
  ItemGift,
} from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

// Utility function to extract store name from URL
const getStoreNameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Remove 'www.' if present
    const cleanHostname = hostname.replace(/^www\./, "");

    // Extract the main domain name (before the first dot)
    const domainParts = cleanHostname.split(".");
    const storeName = domainParts[0];

    // Capitalize first letter
    return storeName.charAt(0).toUpperCase() + storeName.slice(1);
  } catch (error: unknown) {
    console.log(error);
    // If URL parsing fails, try to extract from the string
    const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^/.]+)/);
    if (match && match[1]) {
      return match[1].charAt(0).toUpperCase() + match[1].slice(1);
    }
    return "Store";
  }
};

export const GiftReservations = () => {
  const navigate = useNavigate();
  const { eventId, giftId } = useParams();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<"purchase" | "cash">("purchase");
  const [payment, setPayment] = useState(false);
  const [showJumiaRedirect, setShowJumiaRedirect] = useState(false);
  const [purchaseModal, setPurchaseModal] = useState(false);
  const [isReserving, setIsReserving] = useState(false);
  const [reservationId, setReservationId] = useState<string | null>(null);
  const [itemGift, setItemGift] = useState<ItemGift | null>(null);
  const [eventDetails, setEventDetails] = useState<EventDetailsResponse | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  console.log(reservationId);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        if (!eventId || !giftId) return;

        // Fetch event details and item gift data
        const [eventResponse, itemResponse] = await Promise.all([
          GuestGiftsAPI.getEventDetails(eventId),
          GuestGiftsAPI.getItemGifts(eventId, { page: 1, per_page: 100 }),
        ]);

        setEventDetails(eventResponse);

        // Find the specific item gift
        const gift = itemResponse.gifts.find((g) => g.id === giftId);
        if (gift) {
          setItemGift(gift);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load gift details");
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Check if we have an existing reservation parameter
    const existingReservationId = searchParams.get("existing_reservation");
    if (existingReservationId) {
      // Set the reservation ID and show purchase modal directly
      setReservationId(existingReservationId);
      setPurchaseModal(true);
    }
  }, [eventId, giftId, searchParams]);

  // First reserve the item, then show purchase modal
  const handleContinue = async () => {
    setIsReserving(true);
    try {
      const accessToken = await guestTokenManager.getGuestAccessToken();
      if (!accessToken || !giftId) {
        toast.error("Missing access token or gift information");
        return;
      }

      // Reserve the item gift
      const reservation = await GuestGiftsAPI.reserveItemGift(giftId);
      setReservationId(reservation.id);

      // Store reservation ID for later use
      guestTokenManager.setGuestReservationId(reservation.id);

      toast.success("Gift reserved successfully!");

      // Show purchase modal after successful reservation
      setPurchaseModal(true);
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Failed to reserve gift"
          : "Failed to reserve gift";
      toast.error(errorMsg);
    } finally {
      setIsReserving(false);
    }
  };

  const proceedToPayment = () => {
    setPurchaseModal(false);
    setShowJumiaRedirect(true);
  };

  const handleCloseModal = () => {
    setPurchaseModal(false);
    // Return to viewing gift as guest
    navigate(`/guest/events/${eventId}/gifts`);
  };

  return (
    <div>
      {!showJumiaRedirect ? (
        <div className="pt-4 px-1 md:px-4 pb-17 bg-[linear-gradient(179.05deg,_#FAFAFA_35.71%,_#FFFFFF_99.18%)] mx-1 md:mx-6 rounded-2xl mt-3.5">
          <div className="flex bg-grey-850 rounded-full justify-center p-0.5 mb-6 invisible">
            <button
              className={` py-1.5 invisible rounded-full  w-full text-sm cursor-pointer focus:outline-none transition-colors duration-200 ${
                activeTab === "purchase"
                  ? "text-primary bg-white font-bold"
                  : "text-grey-250"
              }`}
              style={
                activeTab === "purchase"
                  ? { boxShadow: "0px 2px 8px rgba(77, 85, 242, 0.10)" }
                  : { background: "transparent" }
              }
              onClick={() => setActiveTab("purchase")}
            >
              Purchase Item
            </button>
            {/* <button
              className={` py-1.5 rounded-full text-nowrap max-w-[164px]  w-full text-sm cursor-pointer font-medium focus:outline-none transition-colors duration-200 ${
                activeTab === 'cash'
                  ? 'text-primary bg-white font-bold'
                  : 'text-grey-250'
              }`}
              style={
                activeTab === 'cash'
                  ? { boxShadow: '0px 2px 8px rgba(77, 85, 242, 0.10)' }
                  : { background: 'transparent' }
              }
              onClick={() => setActiveTab('cash')}>
              Give Cash Equivalent
            </button> */}
          </div>

          {activeTab === "purchase" && (
            <>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-650"></div>
                  <span className="ml-2 text-primary">
                    Loading gift details...
                  </span>
                </div>
              ) : (
                <>
                  <div className="bg-white rounded-[14px] p-3 ">
                    <div className="flex items-center gap-2">
                      <img src={avatar} alt="icon" />
                      <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                        {itemGift?.item_link
                          ? getStoreNameFromUrl(
                              itemGift.item_link
                            ).toUpperCase()
                          : "STORE"}
                      </p>
                    </div>
                    <p
                      onClick={() => {
                        navigator.clipboard.writeText(
                          itemGift?.item_link || ""
                        );
                        toast("copied");
                      }}
                      className="mt-4.5 text-sm font-medium text-grey-500 bg-grey-850 rounded-full truncate cursor-pointer w-full px-2.5 py-0.5 max-w-[282px]"
                    >
                      {itemGift?.item_link || "No store link available"}
                    </p>
                  </div>
                  <div className="bg-white border border-dashed border-grey-900 mt-4 rounded-[14px] p-3 ">
                    <div className="flex items-center gap-2">
                      <img src={delivery} alt="icon" />
                      <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                        DELIVERY DETAILS
                      </p>
                    </div>
                    <p className="mt-4.5 text-sm font-medium text-dark-blue-100 max-w-[280px]">
                      {eventDetails?.delivery_address ||
                        "No delivery address available"}
                    </p>
                  </div>
                </>
              )}
              <Button
                variant="primary"
                size="md"
                onClick={handleContinue}
                disabled={isReserving}
                className={`text-white mb-8 mt-5 bg-primary-650 disabled:opacity-50 disabled:cursor-not-allowed`}
                iconRight={
                  isReserving ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
                  )
                }
              >
                {isReserving ? "Reserving..." : "Continue"}
              </Button>
            </>
          )}

          {activeTab === "cash" && (
            <>
              <div className="bg-white border border-dashed border-grey-900 mt-4 rounded-[14px] p-3 ">
                <div className="flex items-center gap-2">
                  <img src={delivery} alt="icon" />
                  <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                    GIVE CASH EQUIVALENT{" "}
                  </p>
                </div>
                <p className="mt-4 text-xs text-grey-100">Amount</p>
                <p className="text-2xl font-bold">₦1,650,050.00</p>
                <p className="mt-6 text-xs italic text-grey-950 ">
                  Your payment is securely processed through a trusted gateway,
                  ensuring a smooth and safe gift contribution.{" "}
                </p>
              </div>
              <Button
                variant="primary"
                size="md"
                onClick={() => setPayment(true)}
                className={`text-white mb-20 mt-5 bg-primary-650 `}
                iconRight={
                  <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
                }
              >
                Continue to Payment
              </Button>
            </>
          )}

          {payment && <SuccessPayment />}
        </div>
      ) : (
        <JumiaRedirect />
      )}
      {purchaseModal && (
        <PurchaseModal
          onClose={handleCloseModal}
          onProceedToPayment={proceedToPayment}
        />
      )}
    </div>
  );
};
