import {
  ArrowCircleRight2,
  // Crown,
  Graph,
  Profile2User,
} from 'iconsax-react';
import { Button } from '../../components/button/onboardingButton';
import gift from '../../assets/images/gift-box.png';
// import box from '../../assets/images/box.png';
import { useNavigate } from 'react-router-dom';
import { Footer } from './footer';
// import img from '../../assets/images/res.png';
// import { Head } from '../../components/reuseables/head';
import CreateGuestList from './create-guest-list/create-guest';
import { useEffect, useState } from 'react';
// import { CreateGiftRegistry } from './gift-registry/create-gift-registry';
import { useEventStore } from '../../lib/store/event';
import { CreateGiftRegistry } from './gift-registry/create-gift-registry';
import { Head } from '../../components/reuseables/head';

interface OnboardingProps {
  eventName?: string;
}

export const Onboarding = ({ eventName }: OnboardingProps) => {
  const navigate = useNavigate();
  const { createdEventData, setSelectedEvent } = useEventStore();
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);

  useEffect(() => {
    if (isGuestListModalOpen) {
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    } else {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }

    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    };
  }, [isGuestListModalOpen]);
  return (
    <div className="fixed  inset-0 px-4 md:px-0  [&::-webkit-scrollbar]:hidden overflow-y-auto z-50 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="relative">
        <div
          className="absolute inset-0 h-[520px] md:h-[444px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-50"
          style={{ backgroundSize: 'cover' }}
        />
        <div className="relative z-50">
          <Head />
          <div className="max-w-[560px] w-full mx-auto mb-32 pt-10">
            <div className="flex justify-between items-end mb-8 ">
              <div>
                <div className="flex items-center bg-white w-fit rounded-full pr-4 pl-2 py-1 mb-4">
                  {createdEventData?.banner_preview_url ? (
                    <img
                      src={createdEventData.banner_preview_url}
                      alt="Event banner"
                      className="w-8 h-8 rounded-full mr-2 object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full mr-2 bg-gray-200 flex items-center justify-center">
                      <span className="text-xs text-gray-500">🎉</span>
                    </div>
                  )}
                  <p className="text-xs font-bold text-dark-200 capitalize">
                    {eventName || createdEventData?.title} 🎉
                  </p>
                </div>
                <h3 className="font-medium text-sm md:text-[34px] leading-[114.99999999999999%] capitalize">
                  What would you like
                  <br /> to do first?
                </h3>
              </div>

              <Button
                variant="primary"
                size="md"
                className="bg-primary-650 h-6 md:h-10 text-sm font-semibold text-white "
                iconRight={
                  <ArrowCircleRight2 size="16" color="#fff" variant="Bulk" />
                }
                onClick={() => {
                  if (createdEventData) {
                    setSelectedEvent(createdEventData);
                  }
                  navigate('/');
                }}>
                Go To Dashboard
              </Button>
            </div>

            <div className="flex flex-col md:flex-row justify-between">
              <div className="md:max-w-[270px] w-full ">
                <div
                  onClick={() => setIsGuestListModalOpen(true)}
                  className="bg-white cursor-pointer rounded-[20px] p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden">
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <div className="bg-cus-pink-300 p-3 rounded-full">
                      <Profile2User
                        color="#FF6630"
                        className="w-6 h-6"
                        variant="Bulk"
                      />
                    </div>
                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        Guest Management
                      </p>
                      <p className="text-xs md:text-xl font-medium">
                        Add & manage
                        <br /> guest for your event{' '}
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[-45px] right-[-12px] pointer-events-none ">
                    <Profile2User
                      color="#FCE5DB"
                      className="w-[140px] max-h-[140px]"
                      variant="Bulk"
                    />
                  </div>
                </div>
                <div className="bg-white rounded-[20px] mt-5 p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden opacity-60">
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <div className="flex justify-between items-center w-full">
                      <div className="bg-primary-700 p-3 rounded-full">
                        <Graph
                          className="w-7 h-7"
                          color="#365B96"
                          variant="Bulk"
                        />
                      </div>
                      <div className="bg-white px-3 py-1 rounded-full border border-gray-300">
                        <span className="text-xs font-semibold text-primary-750 tracking-wider">
                          COMING SOON..
                        </span>
                      </div>
                    </div>

                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        BUDGET PLANNER{' '}
                      </p>
                      <p className="text-xs md:text-xl font-medium">
                        Plan a Budget for <br /> your event
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[-25px] right-[-10px] pointer-events-none ">
                    <Graph
                      color="#E5E3FF"
                      className="w-[140px] max-h-[140px]"
                      variant="Bulk"
                    />
                  </div>
                  {/* Coming Soon Watermark */}
                  <div className="absolute inset-0 flex items-start pt-7  justify-end bg-black/5 rounded-[20px]"></div>
                </div>
              </div>
              <div
                // onClick={() => navigate('/create-gift-registry')}
                onClick={() => setIsGiftRegistryModalOpen(true)}
                className="md:max-w-[270px] cursor-pointer  mt-6 md:mt-0 flex flex-col justify-between  w-full rounded-[20px] bg-[linear-gradient(180deg,_#1A22BF_13.41%,_#000059_100%)]">
                <div className="pt-8 pl-4">
                  <h3 className="text-xs text-primary-200 mb-1 tracking-[0.12em]">
                    GIFT REGISTRY
                  </h3>
                  <p className="text-2xl font-medium text-primary-900">
                    Curate gifts for <br /> your event and share with friends 🎉
                  </p>
                </div>
                <img src={gift} alt="gift" />
              </div>
            </div>
            {/* <div className="bg-white mt-5 px-6 pt-6  rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] flex justify-between md:flex-row flex-col">
              <div>
                <h3 className="mb-2 text-cus-orange-100 text-xs tracking-[0.12em]">
                  EVENT WEBSITES
                </h3>
                <p className="text-2xl font-semibold text-cus-orange-200 mb-5">
                  Create a Website <br /> particular to your event
                </p>
                <div className="border border-pink-400 w-fit p-0.5 rounded-full mb-9">
                  <Button
                    variant="primary"
                    size="sm"
                    className="bg-[linear-gradient(184.41deg,_#FFFCFB_3.55%,_#FDF2ED_96.42%)] text-grey-500"
                    iconLeft={
                      <Crown size="12" color="#967F75" variant="Bulk" />
                    }>
                    Coming Soon
                  </Button>
                </div>
              </div>
              <img src={box} alt="box" />
            </div> */}
          </div>
          <Footer />
        </div>
      </div>
      {isGuestListModalOpen && (
        <div className="fixed inset-0 z-50 bg-white overflow-y-auto">
          <div className="min-h-screen">
            <CreateGuestList onClose={() => navigate('/')} />
          </div>
        </div>
      )}
      {isGiftRegistryModalOpen && (
        <CreateGiftRegistry onClose={() => navigate('/')} />
      )}
    </div>
  );
};
